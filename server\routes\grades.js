const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../utils/db');
const { authenticateToken, isTeacherOrAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/grades
// @desc    Get all grades
// @access  Public
router.get('/', async (req, res) => {
  try {
    const grades = await prisma.grade.findMany({
      where: { isActive: true },
      orderBy: { order: 'asc' },
      include: {
        subjects: {
          where: { isActive: true },
          include: {
            units: {
              where: { isActive: true },
              orderBy: { order: 'asc' }
            }
          }
        }
      }
    });

    res.json({
      success: true,
      data: { grades }
    });

  } catch (error) {
    console.error('Get grades error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب الصفوف الدراسية'
    });
  }
});

// @route   GET /api/grades/:id
// @desc    Get single grade
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const grade = await prisma.grade.findUnique({
      where: { id: parseInt(id) },
      include: {
        subjects: {
          where: { isActive: true },
          include: {
            units: {
              where: { isActive: true },
              orderBy: { order: 'asc' },
              include: {
                lessons: {
                  where: { isActive: true },
                  orderBy: { order: 'asc' }
                }
              }
            }
          }
        }
      }
    });

    if (!grade) {
      return res.status(404).json({
        success: false,
        message: 'الصف الدراسي غير موجود'
      });
    }

    res.json({
      success: true,
      data: { grade }
    });

  } catch (error) {
    console.error('Get grade error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب بيانات الصف الدراسي'
    });
  }
});

// @route   POST /api/grades
// @desc    Create new grade
// @access  Private (Admin/Teacher)
router.post('/', [
  authenticateToken,
  isTeacherOrAdmin,
  body('name').notEmpty().trim().withMessage('اسم الصف مطلوب'),
  body('order').isInt({ min: 1 }).withMessage('ترتيب الصف يجب أن يكون رقماً موجباً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name, description, order } = req.body;

    // Check if order already exists
    const existingGrade = await prisma.grade.findUnique({
      where: { order }
    });

    if (existingGrade) {
      return res.status(400).json({
        success: false,
        message: 'هذا الترتيب مستخدم بالفعل'
      });
    }

    const grade = await prisma.grade.create({
      data: {
        name,
        description,
        order
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الصف الدراسي بنجاح',
      data: { grade }
    });

  } catch (error) {
    console.error('Create grade error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إنشاء الصف الدراسي'
    });
  }
});

// @route   PUT /api/grades/:id
// @desc    Update grade
// @access  Private (Admin/Teacher)
router.put('/:id', [
  authenticateToken,
  isTeacherOrAdmin,
  body('name').optional().notEmpty().trim().withMessage('اسم الصف مطلوب'),
  body('order').optional().isInt({ min: 1 }).withMessage('ترتيب الصف يجب أن يكون رقماً موجباً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { name, description, order, isActive } = req.body;

    // Check if grade exists
    const existingGrade = await prisma.grade.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingGrade) {
      return res.status(404).json({
        success: false,
        message: 'الصف الدراسي غير موجود'
      });
    }

    // Check if order already exists (if updating order)
    if (order && order !== existingGrade.order) {
      const gradeWithOrder = await prisma.grade.findUnique({
        where: { order }
      });

      if (gradeWithOrder) {
        return res.status(400).json({
          success: false,
          message: 'هذا الترتيب مستخدم بالفعل'
        });
      }
    }

    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (order !== undefined) updateData.order = order;
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedGrade = await prisma.grade.update({
      where: { id: parseInt(id) },
      data: updateData
    });

    res.json({
      success: true,
      message: 'تم تحديث الصف الدراسي بنجاح',
      data: { grade: updatedGrade }
    });

  } catch (error) {
    console.error('Update grade error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تحديث الصف الدراسي'
    });
  }
});

// @route   DELETE /api/grades/:id
// @desc    Delete grade (soft delete)
// @access  Private (Admin/Teacher)
router.delete('/:id', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const grade = await prisma.grade.findUnique({
      where: { id: parseInt(id) }
    });

    if (!grade) {
      return res.status(404).json({
        success: false,
        message: 'الصف الدراسي غير موجود'
      });
    }

    // Soft delete
    await prisma.grade.update({
      where: { id: parseInt(id) },
      data: { isActive: false }
    });

    res.json({
      success: true,
      message: 'تم حذف الصف الدراسي بنجاح'
    });

  } catch (error) {
    console.error('Delete grade error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء حذف الصف الدراسي'
    });
  }
});

module.exports = router;