const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../utils/db');
const { authenticateToken, isTeacherOrAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/quizzes
// @desc    Get all quizzes
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { lessonId } = req.query;
    const whereClause = { isActive: true };

    if (lessonId) {
      whereClause.lessonId = parseInt(lessonId);
    }

    const quizzes = await prisma.quiz.findMany({
      where: whereClause,
      include: {
        lesson: {
          include: {
            unit: {
              include: {
                subject: {
                  include: { grade: true }
                }
              }
            }
          }
        },
        questions: {
          select: {
            id: true,
            question: true,
            type: true,
            points: true,
            order: true
          },
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      success: true,
      data: { quizzes }
    });

  } catch (error) {
    console.error('Get quizzes error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب الاختبارات'
    });
  }
});

// @route   GET /api/quizzes/:id
// @desc    Get single quiz with questions
// @access  Public (but answers are hidden)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const quiz = await prisma.quiz.findUnique({
      where: { id: parseInt(id) },
      include: {
        lesson: {
          include: {
            unit: {
              include: {
                subject: {
                  include: { grade: true }
                }
              }
            }
          }
        },
        questions: {
          select: {
            id: true,
            question: true,
            type: true,
            options: true,
            points: true,
            order: true,
            explanation: true
          },
          orderBy: { order: 'asc' }
        },
        results: {
          where: req.user ? { userId: req.user.id } : {},
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: 'الاختبار غير موجود'
      });
    }

    // Hide correct answers from students
    if (!req.user || req.user.role === 'STUDENT') {
      quiz.questions = quiz.questions.map(question => ({
        ...question,
        correctAnswer: undefined,
        explanation: undefined
      }));
    }

    res.json({
      success: true,
      data: { quiz }
    });

  } catch (error) {
    console.error('Get quiz error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب بيانات الاختبار'
    });
  }
});

// @route   POST /api/quizzes
// @desc    Create new quiz
// @access  Private (Admin/Teacher)
router.post('/', [
  authenticateToken,
  isTeacherOrAdmin,
  body('title').notEmpty().trim().withMessage('عنوان الاختبار مطلوب'),
  body('lessonId').isInt({ min: 1 }).withMessage('معرف الدرس مطلوب'),
  body('passingScore').isInt({ min: 0, max: 100 }).withMessage('درجة النجاح يجب أن تكون بين 0 و 100')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { title, description, lessonId, timeLimit, passingScore } = req.body;

    // Check if lesson exists
    const lesson = await prisma.lesson.findUnique({
      where: { id: parseInt(lessonId) }
    });

    if (!lesson) {
      return res.status(400).json({
        success: false,
        message: 'الدرس غير موجود'
      });
    }

    const quiz = await prisma.quiz.create({
      data: {
        title,
        description,
        lessonId: parseInt(lessonId),
        timeLimit: timeLimit ? parseInt(timeLimit) : null,
        passingScore: parseInt(passingScore)
      },
      include: {
        lesson: {
          include: {
            unit: {
              include: {
                subject: {
                  include: { grade: true }
                }
              }
            }
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الاختبار بنجاح',
      data: { quiz }
    });

  } catch (error) {
    console.error('Create quiz error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إنشاء الاختبار'
    });
  }
});

// @route   POST /api/quizzes/:id/submit
// @desc    Submit quiz answers
// @access  Private (Student)
router.post('/:id/submit', [
  authenticateToken,
  body('answers').isObject().withMessage('الإجابات مطلوبة'),
  body('timeSpent').optional().isInt({ min: 0 }).withMessage('الوقت المستغرق يجب أن يكون رقماً موجباً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { answers, timeSpent } = req.body;

    // Get quiz with questions
    const quiz = await prisma.quiz.findUnique({
      where: { id: parseInt(id) },
      include: {
        questions: {
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: 'الاختبار غير موجود'
      });
    }

    // Check if user already submitted this quiz
    const existingResult = await prisma.quizResult.findFirst({
      where: {
        userId: req.user.id,
        quizId: parseInt(id)
      }
    });

    if (existingResult) {
      return res.status(400).json({
        success: false,
        message: 'لقد قمت بتقديم هذا الاختبار من قبل'
      });
    }

    // Calculate score
    let totalPoints = 0;
    let earnedPoints = 0;

    quiz.questions.forEach(question => {
      totalPoints += question.points;
      const userAnswer = answers[question.id.toString()];

      if (userAnswer && userAnswer.toLowerCase() === question.correctAnswer.toLowerCase()) {
        earnedPoints += question.points;
      }
    });

    const score = Math.round((earnedPoints / totalPoints) * 100);
    const isPassed = score >= quiz.passingScore;

    // Save result
    const result = await prisma.quizResult.create({
      data: {
        userId: req.user.id,
        quizId: parseInt(id),
        score,
        answers,
        timeSpent: timeSpent ? parseInt(timeSpent) : null,
        isPassed
      }
    });

    res.json({
      success: true,
      message: isPassed ? 'تهانينا! لقد نجحت في الاختبار' : 'للأسف لم تحصل على درجة النجاح',
      data: {
        result: {
          id: result.id,
          score,
          isPassed,
          totalPoints,
          earnedPoints,
          passingScore: quiz.passingScore
        }
      }
    });

  } catch (error) {
    console.error('Submit quiz error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تقديم الاختبار'
    });
  }
});

// @route   GET /api/quizzes/:id/results
// @desc    Get quiz results (for teachers/admins)
// @access  Private (Admin/Teacher)
router.get('/:id/results', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const results = await prisma.quizResult.findMany({
      where: { quizId: parseInt(id) },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      success: true,
      data: { results }
    });

  } catch (error) {
    console.error('Get quiz results error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب نتائج الاختبار'
    });
  }
});

// @route   DELETE /api/quizzes/:id
// @desc    Delete quiz (soft delete)
// @access  Private (Admin/Teacher)
router.delete('/:id', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const quiz = await prisma.quiz.findUnique({
      where: { id: parseInt(id) }
    });

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: 'الاختبار غير موجود'
      });
    }

    // Soft delete
    await prisma.quiz.update({
      where: { id: parseInt(id) },
      data: { isActive: false }
    });

    res.json({
      success: true,
      message: 'تم حذف الاختبار بنجاح'
    });

  } catch (error) {
    console.error('Delete quiz error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء حذف الاختبار'
    });
  }
});

module.exports = router;