const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../utils/db');
const { authenticateToken, isTeacherOrAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/units
// @desc    Get all units
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { subjectId } = req.query;
    const whereClause = { isActive: true };

    if (subjectId) {
      whereClause.subjectId = parseInt(subjectId);
    }

    const units = await prisma.unit.findMany({
      where: whereClause,
      include: {
        subject: {
          include: { grade: true }
        },
        lessons: {
          where: { isActive: true },
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { order: 'asc' }
    });

    res.json({
      success: true,
      data: { units }
    });

  } catch (error) {
    console.error('Get units error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب الوحدات الدراسية'
    });
  }
});

// @route   GET /api/units/:id
// @desc    Get single unit
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const unit = await prisma.unit.findUnique({
      where: { id: parseInt(id) },
      include: {
        subject: {
          include: { grade: true }
        },
        lessons: {
          where: { isActive: true },
          orderBy: { order: 'asc' },
          include: {
            quizzes: {
              where: { isActive: true }
            }
          }
        }
      }
    });

    if (!unit) {
      return res.status(404).json({
        success: false,
        message: 'الوحدة الدراسية غير موجودة'
      });
    }

    res.json({
      success: true,
      data: { unit }
    });

  } catch (error) {
    console.error('Get unit error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب بيانات الوحدة الدراسية'
    });
  }
});

// @route   POST /api/units
// @desc    Create new unit
// @access  Private (Admin/Teacher)
router.post('/', [
  authenticateToken,
  isTeacherOrAdmin,
  body('title').notEmpty().trim().withMessage('عنوان الوحدة مطلوب'),
  body('subjectId').isInt({ min: 1 }).withMessage('معرف المادة مطلوب'),
  body('order').isInt({ min: 1 }).withMessage('ترتيب الوحدة مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { title, description, subjectId, order } = req.body;

    // Check if subject exists
    const subject = await prisma.subject.findUnique({
      where: { id: parseInt(subjectId) }
    });

    if (!subject) {
      return res.status(400).json({
        success: false,
        message: 'المادة الدراسية غير موجودة'
      });
    }

    // Check if order already exists for this subject
    const existingUnit = await prisma.unit.findFirst({
      where: {
        subjectId: parseInt(subjectId),
        order: parseInt(order)
      }
    });

    if (existingUnit) {
      return res.status(400).json({
        success: false,
        message: 'هذا الترتيب مستخدم بالفعل في هذه المادة'
      });
    }

    const unit = await prisma.unit.create({
      data: {
        title,
        description,
        subjectId: parseInt(subjectId),
        order: parseInt(order)
      },
      include: {
        subject: {
          include: { grade: true }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الوحدة الدراسية بنجاح',
      data: { unit }
    });

  } catch (error) {
    console.error('Create unit error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إنشاء الوحدة الدراسية'
    });
  }
});

// @route   PUT /api/units/:id
// @desc    Update unit
// @access  Private (Admin/Teacher)
router.put('/:id', [
  authenticateToken,
  isTeacherOrAdmin,
  body('title').optional().notEmpty().trim().withMessage('عنوان الوحدة مطلوب'),
  body('subjectId').optional().isInt({ min: 1 }).withMessage('معرف المادة مطلوب'),
  body('order').optional().isInt({ min: 1 }).withMessage('ترتيب الوحدة مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { title, description, subjectId, order, isActive } = req.body;

    // Check if unit exists
    const existingUnit = await prisma.unit.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingUnit) {
      return res.status(404).json({
        success: false,
        message: 'الوحدة الدراسية غير موجودة'
      });
    }

    // Check if subject exists (if updating subjectId)
    if (subjectId) {
      const subject = await prisma.subject.findUnique({
        where: { id: parseInt(subjectId) }
      });

      if (!subject) {
        return res.status(400).json({
          success: false,
          message: 'المادة الدراسية غير موجودة'
        });
      }
    }

    // Check if order already exists (if updating order)
    if (order && (order !== existingUnit.order || subjectId)) {
      const targetSubjectId = subjectId ? parseInt(subjectId) : existingUnit.subjectId;
      const unitWithOrder = await prisma.unit.findFirst({
        where: {
          subjectId: targetSubjectId,
          order: parseInt(order),
          id: { not: parseInt(id) }
        }
      });

      if (unitWithOrder) {
        return res.status(400).json({
          success: false,
          message: 'هذا الترتيب مستخدم بالفعل في هذه المادة'
        });
      }
    }

    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (subjectId !== undefined) updateData.subjectId = parseInt(subjectId);
    if (order !== undefined) updateData.order = parseInt(order);
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedUnit = await prisma.unit.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        subject: {
          include: { grade: true }
        }
      }
    });

    res.json({
      success: true,
      message: 'تم تحديث الوحدة الدراسية بنجاح',
      data: { unit: updatedUnit }
    });

  } catch (error) {
    console.error('Update unit error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تحديث الوحدة الدراسية'
    });
  }
});

// @route   DELETE /api/units/:id
// @desc    Delete unit (soft delete)
// @access  Private (Admin/Teacher)
router.delete('/:id', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const unit = await prisma.unit.findUnique({
      where: { id: parseInt(id) }
    });

    if (!unit) {
      return res.status(404).json({
        success: false,
        message: 'الوحدة الدراسية غير موجودة'
      });
    }

    // Soft delete
    await prisma.unit.update({
      where: { id: parseInt(id) },
      data: { isActive: false }
    });

    res.json({
      success: true,
      message: 'تم حذف الوحدة الدراسية بنجاح'
    });

  } catch (error) {
    console.error('Delete unit error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء حذف الوحدة الدراسية'
    });
  }
});

module.exports = router;