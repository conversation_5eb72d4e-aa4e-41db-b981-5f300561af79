// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          Int       @id @default(autoincrement())
  email       String    @unique
  password    String
  firstName   String
  lastName    String
  phone       String?
  avatar      String?
  role        UserRole  @default(STUDENT)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  enrollments Enrollment[]
  quizResults QuizResult[]
  comments    Comment[]
  payments    Payment[]

  @@map("users")
}

model Grade {
  id          Int       @id @default(autoincrement())
  name        String    // "الصف الأول الإعدادي", "الصف الثاني الإعدادي", "الصف الثالث الإعدادي"
  description String?
  order       Int       @unique
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  subjects    Subject[]

  @@map("grades")
}

model Subject {
  id          Int       @id @default(autoincrement())
  name        String    // "الدراسات الاجتماعية"
  description String?
  gradeId     Int
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  grade       Grade     @relation(fields: [gradeId], references: [id])
  units       Unit[]

  @@map("subjects")
}

model Unit {
  id          Int       @id @default(autoincrement())
  title       String
  description String?
  subjectId   Int
  order       Int
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  subject     Subject   @relation(fields: [subjectId], references: [id])
  lessons     Lesson[]

  @@map("units")
}

model Lesson {
  id          Int         @id @default(autoincrement())
  title       String
  description String?
  content     String?
  videoUrl    String?
  pdfUrl      String?
  duration    Int?        // in minutes
  unitId      Int
  order       Int
  lessonType  LessonType  @default(VIDEO)
  isActive    Boolean     @default(true)
  isFree      Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  unit        Unit        @relation(fields: [unitId], references: [id])
  enrollments Enrollment[]
  quizzes     Quiz[]
  comments    Comment[]

  @@map("lessons")
}

model Subscription {
  id          Int              @id @default(autoincrement())
  name        String
  description String?
  price       Decimal          @db.Decimal(10, 2)
  duration    Int              // in days
  features    Json?            // JSON array of features
  isActive    Boolean          @default(true)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  enrollments Enrollment[]
  payments    Payment[]

  @@map("subscriptions")
}

model Enrollment {
  id             Int            @id @default(autoincrement())
  userId         Int
  subscriptionId Int
  lessonId       Int?
  startDate      DateTime       @default(now())
  endDate        DateTime
  isActive       Boolean        @default(true)
  progress       Int            @default(0) // percentage
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  user           User           @relation(fields: [userId], references: [id])
  subscription   Subscription   @relation(fields: [subscriptionId], references: [id])
  lesson         Lesson?        @relation(fields: [lessonId], references: [id])

  @@map("enrollments")
}

model Quiz {
  id          Int          @id @default(autoincrement())
  title       String
  description String?
  lessonId    Int
  timeLimit   Int?         // in minutes
  passingScore Int         @default(50) // percentage
  isActive    Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  lesson      Lesson       @relation(fields: [lessonId], references: [id])
  questions   Question[]
  results     QuizResult[]

  @@map("quizzes")
}

model Question {
  id          Int            @id @default(autoincrement())
  quizId      Int
  question    String
  type        QuestionType   @default(MULTIPLE_CHOICE)
  options     Json?          // JSON array for multiple choice options
  correctAnswer String
  explanation String?
  points      Int            @default(1)
  order       Int
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  quiz        Quiz           @relation(fields: [quizId], references: [id])

  @@map("questions")
}

model QuizResult {
  id          Int       @id @default(autoincrement())
  userId      Int
  quizId      Int
  score       Int       // percentage
  answers     Json      // JSON object with question_id: answer pairs
  timeSpent   Int?      // in seconds
  isPassed    Boolean   @default(false)
  createdAt   DateTime  @default(now())

  // Relations
  user        User      @relation(fields: [userId], references: [id])
  quiz        Quiz      @relation(fields: [quizId], references: [id])

  @@map("quiz_results")
}

model Payment {
  id             Int            @id @default(autoincrement())
  userId         Int
  subscriptionId Int
  amount         Decimal        @db.Decimal(10, 2)
  currency       String         @default("EGP")
  status         PaymentStatus  @default(PENDING)
  paymentMethod  String?
  transactionId  String?        @unique
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  user           User           @relation(fields: [userId], references: [id])
  subscription   Subscription   @relation(fields: [subscriptionId], references: [id])

  @@map("payments")
}

model Comment {
  id          Int       @id @default(autoincrement())
  userId      Int
  lessonId    Int
  content     String
  isApproved  Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  user        User      @relation(fields: [userId], references: [id])
  lesson      Lesson    @relation(fields: [lessonId], references: [id])

  @@map("comments")
}

model LiveSession {
  id          Int       @id @default(autoincrement())
  title       String
  description String?
  scheduledAt DateTime
  duration    Int       // in minutes
  meetingUrl  String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("live_sessions")
}

// Enums
enum UserRole {
  STUDENT
  ADMIN
  TEACHER
}

enum LessonType {
  VIDEO
  PDF
  LIVE
  QUIZ
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SHORT_ANSWER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}