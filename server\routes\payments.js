const express = require('express');
const { prisma } = require('../utils/db');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

router.get('/my', authenticateToken, async (req, res) => {
  try {
    const payments = await prisma.payment.findMany({
      where: { userId: req.user.id },
      include: { subscription: true }
    });
    res.json({ success: true, data: { payments } });
  } catch (error) {
    res.status(500).json({ success: false, message: 'حدث خطأ' });
  }
});

module.exports = router;