const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../utils/db');
const { authenticateToken, isTeacherOrAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/questions
// @desc    Get all questions for a quiz
// @access  Private (Admin/Teacher)
router.get('/', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { quizId } = req.query;
    
    if (!quizId) {
      return res.status(400).json({
        success: false,
        message: 'معرف الاختبار مطلوب'
      });
    }

    const questions = await prisma.question.findMany({
      where: { quizId: parseInt(quizId) },
      include: {
        quiz: {
          include: {
            lesson: {
              include: {
                unit: {
                  include: {
                    subject: {
                      include: { grade: true }
                    }
                  }
                }
              }
            }
          }
        }
      },
      orderBy: { order: 'asc' }
    });

    res.json({
      success: true,
      data: { questions }
    });

  } catch (error) {
    console.error('Get questions error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب الأسئلة'
    });
  }
});

// @route   POST /api/questions
// @desc    Create new question
// @access  Private (Admin/Teacher)
router.post('/', [
  authenticateToken,
  isTeacherOrAdmin,
  body('quizId').isInt({ min: 1 }).withMessage('معرف الاختبار مطلوب'),
  body('question').notEmpty().trim().withMessage('نص السؤال مطلوب'),
  body('type').isIn(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SHORT_ANSWER']).withMessage('نوع السؤال غير صالح'),
  body('correctAnswer').notEmpty().trim().withMessage('الإجابة الصحيحة مطلوبة'),
  body('points').isInt({ min: 1 }).withMessage('نقاط السؤال يجب أن تكون رقماً موجباً'),
  body('order').isInt({ min: 1 }).withMessage('ترتيب السؤال مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { quizId, question, type, options, correctAnswer, explanation, points, order } = req.body;

    // Check if quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: parseInt(quizId) }
    });

    if (!quiz) {
      return res.status(400).json({
        success: false,
        message: 'الاختبار غير موجود'
      });
    }

    // Check if order already exists for this quiz
    const existingQuestion = await prisma.question.findFirst({
      where: { 
        quizId: parseInt(quizId),
        order: parseInt(order)
      }
    });

    if (existingQuestion) {
      return res.status(400).json({
        success: false,
        message: 'هذا الترتيب مستخدم بالفعل في هذا الاختبار'
      });
    }

    // Validate options for multiple choice questions
    let parsedOptions = null;
    if (type === 'MULTIPLE_CHOICE') {
      if (!options) {
        return res.status(400).json({
          success: false,
          message: 'خيارات الإجابة مطلوبة للأسئلة متعددة الخيارات'
        });
      }
      
      try {
        parsedOptions = typeof options === 'string' ? JSON.parse(options) : options;
        if (!Array.isArray(parsedOptions) || parsedOptions.length < 2) {
          return res.status(400).json({
            success: false,
            message: 'يجب أن تحتوي الأسئلة متعددة الخيارات على خيارين على الأقل'
          });
        }
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: 'تنسيق خيارات الإجابة غير صالح'
        });
      }
    }

    const newQuestion = await prisma.question.create({
      data: {
        quizId: parseInt(quizId),
        question,
        type,
        options: parsedOptions,
        correctAnswer,
        explanation,
        points: parseInt(points),
        order: parseInt(order)
      },
      include: {
        quiz: {
          include: {
            lesson: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء السؤال بنجاح',
      data: { question: newQuestion }
    });

  } catch (error) {
    console.error('Create question error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إنشاء السؤال'
    });
  }
});

// @route   PUT /api/questions/:id
// @desc    Update question
// @access  Private (Admin/Teacher)
router.put('/:id', [
  authenticateToken,
  isTeacherOrAdmin,
  body('question').optional().notEmpty().trim().withMessage('نص السؤال مطلوب'),
  body('type').optional().isIn(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SHORT_ANSWER']).withMessage('نوع السؤال غير صالح'),
  body('correctAnswer').optional().notEmpty().trim().withMessage('الإجابة الصحيحة مطلوبة'),
  body('points').optional().isInt({ min: 1 }).withMessage('نقاط السؤال يجب أن تكون رقماً موجباً'),
  body('order').optional().isInt({ min: 1 }).withMessage('ترتيب السؤال مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { question, type, options, correctAnswer, explanation, points, order } = req.body;

    // Check if question exists
    const existingQuestion = await prisma.question.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingQuestion) {
      return res.status(404).json({
        success: false,
        message: 'السؤال غير موجود'
      });
    }

    // Check if order already exists (if updating order)
    if (order && order !== existingQuestion.order) {
      const questionWithOrder = await prisma.question.findFirst({
        where: { 
          quizId: existingQuestion.quizId,
          order: parseInt(order),
          id: { not: parseInt(id) }
        }
      });

      if (questionWithOrder) {
        return res.status(400).json({
          success: false,
          message: 'هذا الترتيب مستخدم بالفعل في هذا الاختبار'
        });
      }
    }

    const updateData = {};
    if (question !== undefined) updateData.question = question;
    if (type !== undefined) updateData.type = type;
    if (correctAnswer !== undefined) updateData.correctAnswer = correctAnswer;
    if (explanation !== undefined) updateData.explanation = explanation;
    if (points !== undefined) updateData.points = parseInt(points);
    if (order !== undefined) updateData.order = parseInt(order);

    // Handle options
    if (options !== undefined) {
      if (type === 'MULTIPLE_CHOICE' || existingQuestion.type === 'MULTIPLE_CHOICE') {
        try {
          updateData.options = typeof options === 'string' ? JSON.parse(options) : options;
        } catch (error) {
          return res.status(400).json({
            success: false,
            message: 'تنسيق خيارات الإجابة غير صالح'
          });
        }
      } else {
        updateData.options = null;
      }
    }

    const updatedQuestion = await prisma.question.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        quiz: {
          include: {
            lesson: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'تم تحديث السؤال بنجاح',
      data: { question: updatedQuestion }
    });

  } catch (error) {
    console.error('Update question error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تحديث السؤال'
    });
  }
});

// @route   DELETE /api/questions/:id
// @desc    Delete question
// @access  Private (Admin/Teacher)
router.delete('/:id', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const question = await prisma.question.findUnique({
      where: { id: parseInt(id) }
    });

    if (!question) {
      return res.status(404).json({
        success: false,
        message: 'السؤال غير موجود'
      });
    }

    // Delete question (hard delete since it's part of quiz structure)
    await prisma.question.delete({
      where: { id: parseInt(id) }
    });

    res.json({
      success: true,
      message: 'تم حذف السؤال بنجاح'
    });

  } catch (error) {
    console.error('Delete question error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء حذف السؤال'
    });
  }
});

module.exports = router;
