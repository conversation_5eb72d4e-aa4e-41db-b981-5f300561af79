const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../utils/db');
const { authenticateToken, isTeacherOrAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/subscriptions
// @desc    Get all subscriptions
// @access  Public
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { isActive: true },
      orderBy: { price: 'asc' }
    });

    res.json({
      success: true,
      data: { subscriptions }
    });

  } catch (error) {
    console.error('Get subscriptions error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب خطط الاشتراك'
    });
  }
});

// @route   GET /api/subscriptions/:id
// @desc    Get single subscription
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findUnique({
      where: { id: parseInt(id) },
      include: {
        enrollments: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        payments: {
          where: { status: 'COMPLETED' },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'خطة الاشتراك غير موجودة'
      });
    }

    res.json({
      success: true,
      data: { subscription }
    });

  } catch (error) {
    console.error('Get subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب بيانات خطة الاشتراك'
    });
  }
});

// @route   POST /api/subscriptions
// @desc    Create new subscription
// @access  Private (Admin/Teacher)
router.post('/', [
  authenticateToken,
  isTeacherOrAdmin,
  body('name').notEmpty().trim().withMessage('اسم خطة الاشتراك مطلوب'),
  body('price').isDecimal({ decimal_digits: '0,2' }).withMessage('السعر يجب أن يكون رقماً صالحاً'),
  body('duration').isInt({ min: 1 }).withMessage('مدة الاشتراك يجب أن تكون رقماً موجباً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name, description, price, duration, features } = req.body;

    const subscription = await prisma.subscription.create({
      data: {
        name,
        description,
        price: parseFloat(price),
        duration: parseInt(duration),
        features: features ? JSON.parse(features) : null
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء خطة الاشتراك بنجاح',
      data: { subscription }
    });

  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إنشاء خطة الاشتراك'
    });
  }
});

// @route   PUT /api/subscriptions/:id
// @desc    Update subscription
// @access  Private (Admin/Teacher)
router.put('/:id', [
  authenticateToken,
  isTeacherOrAdmin,
  body('name').optional().notEmpty().trim().withMessage('اسم خطة الاشتراك مطلوب'),
  body('price').optional().isDecimal({ decimal_digits: '0,2' }).withMessage('السعر يجب أن يكون رقماً صالحاً'),
  body('duration').optional().isInt({ min: 1 }).withMessage('مدة الاشتراك يجب أن تكون رقماً موجباً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { name, description, price, duration, features, isActive } = req.body;

    // Check if subscription exists
    const existingSubscription = await prisma.subscription.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingSubscription) {
      return res.status(404).json({
        success: false,
        message: 'خطة الاشتراك غير موجودة'
      });
    }

    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) updateData.price = parseFloat(price);
    if (duration !== undefined) updateData.duration = parseInt(duration);
    if (features !== undefined) updateData.features = features ? JSON.parse(features) : null;
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedSubscription = await prisma.subscription.update({
      where: { id: parseInt(id) },
      data: updateData
    });

    res.json({
      success: true,
      message: 'تم تحديث خطة الاشتراك بنجاح',
      data: { subscription: updatedSubscription }
    });

  } catch (error) {
    console.error('Update subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تحديث خطة الاشتراك'
    });
  }
});

// @route   DELETE /api/subscriptions/:id
// @desc    Delete subscription (soft delete)
// @access  Private (Admin/Teacher)
router.delete('/:id', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findUnique({
      where: { id: parseInt(id) }
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'خطة الاشتراك غير موجودة'
      });
    }

    // Soft delete
    await prisma.subscription.update({
      where: { id: parseInt(id) },
      data: { isActive: false }
    });

    res.json({
      success: true,
      message: 'تم حذف خطة الاشتراك بنجاح'
    });

  } catch (error) {
    console.error('Delete subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء حذف خطة الاشتراك'
    });
  }
});

module.exports = router;