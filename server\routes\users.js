const express = require('express');
const { prisma } = require('../utils/db');
const { authenticateToken, isAdmin } = require('../middleware/auth');

const router = express.Router();

router.get('/', [authenticateToken, isAdmin], async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: { id: true, email: true, firstName: true, lastName: true, role: true, isActive: true, createdAt: true }
    });
    res.json({ success: true, data: { users } });
  } catch (error) {
    res.status(500).json({ success: false, message: 'حدث خطأ' });
  }
});

module.exports = router;