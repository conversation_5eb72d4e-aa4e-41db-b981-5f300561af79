# منصة أستاذ سيف التعليمية

منصة تعليمية شاملة للدراسات الاجتماعية للمرحلة الإعدادية

## 🚀 المميزات

- **نظام مصادقة آمن** - تسجيل دخول وإنشاء حسابات للطلاب
- **محتوى تعليمي منظم** - دروس مقسمة حسب الصفوف والوحدات
- **اختبارات تفاعلية** - نظام اختبارات شامل مع تقييم فوري
- **نظام اشتراكات** - خطط اشتراك متنوعة للطلاب
- **جلسات مباشرة** - إمكانية عقد جلسات تعليمية مباشرة
- **لوحة تحكم إدارية** - إدارة شاملة للمحتوى والمستخدمين

## 🛠️ التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **PostgreSQL** - قاعدة البيانات
- **Prisma** - ORM لإدارة قاعدة البيانات
- **JWT** - نظام المصادقة
- **Socket.io** - التواصل المباشر

### Frontend
- **React 19** - مكتبة واجهة المستخدم
- **TypeScript** - لغة البرمجة
- **Tailwind CSS** - إطار عمل التصميم
- **React Router** - نظام التوجيه
- **Axios** - للتواصل مع API

## 📦 التثبيت والتشغيل

### متطلبات النظام
- Node.js (v18 أو أحدث)
- PostgreSQL (v13 أو أحدث)
- npm أو yarn

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd ostaz-saif-platform
```

### 2. إعداد الخادم (Backend)
```bash
cd server
npm install
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb ostaz_saif_db

# تشغيل migrations
npx prisma migrate dev
npx prisma generate
```

### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتحديث قيم قاعدة البيانات في ملف .env
```

### 5. تشغيل الخادم
```bash
npm run dev
```

### 6. إعداد الواجهة الأمامية (Frontend)
```bash
cd ../client
npm install
npm start
```

## 📁 هيكل المشروع

```
ostaz-saif-platform/
├── server/                 # الخادم (Backend)
│   ├── controllers/        # منطق التحكم
│   ├── middleware/         # Middleware functions
│   ├── prisma/            # إعدادات قاعدة البيانات
│   ├── routes/            # مسارات API
│   ├── uploads/           # ملفات مرفوعة
│   └── utils/             # أدوات مساعدة
├── client/                # الواجهة الأمامية (Frontend)
│   ├── public/            # ملفات عامة
│   └── src/               # كود المصدر
│       ├── components/    # مكونات React
│       ├── pages/         # صفحات التطبيق
│       ├── services/      # خدمات API
│       └── utils/         # أدوات مساعدة
└── README.md
```

## 🔧 API Endpoints

### المصادقة
- `POST /api/auth/register` - إنشاء حساب جديد
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - الحصول على بيانات المستخدم الحالي
- `PUT /api/auth/update-profile` - تحديث الملف الشخصي

### الصفوف والمواد
- `GET /api/grades` - جلب جميع الصفوف
- `GET /api/subjects` - جلب جميع المواد
- `GET /api/lessons` - جلب جميع الدروس

### الاشتراكات والمدفوعات
- `GET /api/subscriptions` - جلب خطط الاشتراك
- `GET /api/payments/my` - جلب مدفوعات المستخدم

## 👥 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **الموقع**: [ostaz-saif.com](https://ostaz-saif.com)
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20 xxx xxx xxxx

---

**منصة أستاذ سيف التعليمية** - تعلم الدراسات الاجتماعية بطريقة تفاعلية وممتعة! 🎓
