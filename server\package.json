{"name": "ostaz-saif-server", "version": "1.0.0", "description": "منصة أستاذ سيف التعليمية - الخادم", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["education", "platform", "nodejs", "express", "postgresql"], "author": "Ostaz <PERSON>", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "express-validator": "^7.0.1", "socket.io": "^4.7.5", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.2"}}