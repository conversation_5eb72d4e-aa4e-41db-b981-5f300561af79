const express = require('express');
const { prisma } = require('../utils/db');

const router = express.Router();

router.get('/', async (req, res) => {
  try {
    const comments = await prisma.comment.findMany({
      where: { isApproved: true },
      include: { user: { select: { firstName: true, lastName: true } }, lesson: true }
    });
    res.json({ success: true, data: { comments } });
  } catch (error) {
    res.status(500).json({ success: false, message: 'حدث خطأ' });
  }
});

module.exports = router;