const express = require('express');
const { prisma } = require('../utils/db');

const router = express.Router();

router.get('/', async (req, res) => {
  try {
    const sessions = await prisma.liveSession.findMany({
      where: { isActive: true },
      orderBy: { scheduledAt: 'asc' }
    });
    res.json({ success: true, data: { sessions } });
  } catch (error) {
    res.status(500).json({ success: false, message: 'حدث خطأ' });
  }
});

module.exports = router;