const express = require('express');
const { body, validationResult } = require('express-validator');
const multer = require('multer');
const path = require('path');
const { prisma } = require('../utils/db');
const { authenticateToken, isTeacherOrAdmin } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    if (file.fieldname === 'video') {
      cb(null, 'uploads/videos/');
    } else if (file.fieldname === 'pdf') {
      cb(null, 'uploads/pdfs/');
    } else {
      cb(null, 'uploads/');
    }
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 50000000 // 50MB
  },
  fileFilter: function (req, file, cb) {
    if (file.fieldname === 'video') {
      if (file.mimetype.startsWith('video/')) {
        cb(null, true);
      } else {
        cb(new Error('يجب أن يكون الملف فيديو'));
      }
    } else if (file.fieldname === 'pdf') {
      if (file.mimetype === 'application/pdf') {
        cb(null, true);
      } else {
        cb(new Error('يجب أن يكون الملف PDF'));
      }
    } else {
      cb(null, true);
    }
  }
});

// @route   GET /api/lessons
// @desc    Get all lessons
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { unitId, isFree } = req.query;
    const whereClause = { isActive: true };

    if (unitId) {
      whereClause.unitId = parseInt(unitId);
    }

    if (isFree !== undefined) {
      whereClause.isFree = isFree === 'true';
    }

    const lessons = await prisma.lesson.findMany({
      where: whereClause,
      include: {
        unit: {
          include: {
            subject: {
              include: { grade: true }
            }
          }
        },
        quizzes: {
          where: { isActive: true },
          select: {
            id: true,
            title: true,
            timeLimit: true,
            passingScore: true
          }
        },
        comments: {
          where: { isApproved: true },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      },
      orderBy: { order: 'asc' }
    });

    res.json({
      success: true,
      data: { lessons }
    });

  } catch (error) {
    console.error('Get lessons error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب الدروس'
    });
  }
});

// @route   GET /api/lessons/:id
// @desc    Get single lesson
// @access  Public (but content may be restricted)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const lesson = await prisma.lesson.findUnique({
      where: { id: parseInt(id) },
      include: {
        unit: {
          include: {
            subject: {
              include: { grade: true }
            }
          }
        },
        quizzes: {
          where: { isActive: true },
          include: {
            questions: {
              select: {
                id: true,
                question: true,
                type: true,
                options: true,
                points: true,
                order: true
              },
              orderBy: { order: 'asc' }
            }
          }
        },
        comments: {
          where: { isApproved: true },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: 'الدرس غير موجود'
      });
    }

    // If lesson is not free, check if user has access (implement subscription check here)
    if (!lesson.isFree) {
      // TODO: Check user subscription/enrollment
      // For now, we'll return the lesson without video/pdf URLs for non-free content
      if (!req.user) {
        lesson.videoUrl = null;
        lesson.pdfUrl = null;
        lesson.content = lesson.content ? lesson.content.substring(0, 200) + '...' : null;
      }
    }

    res.json({
      success: true,
      data: { lesson }
    });

  } catch (error) {
    console.error('Get lesson error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب بيانات الدرس'
    });
  }
});

// @route   POST /api/lessons
// @desc    Create new lesson
// @access  Private (Admin/Teacher)
router.post('/', [
  authenticateToken,
  isTeacherOrAdmin,
  upload.fields([
    { name: 'video', maxCount: 1 },
    { name: 'pdf', maxCount: 1 }
  ]),
  body('title').notEmpty().trim().withMessage('عنوان الدرس مطلوب'),
  body('unitId').isInt({ min: 1 }).withMessage('معرف الوحدة مطلوب'),
  body('order').isInt({ min: 1 }).withMessage('ترتيب الدرس مطلوب'),
  body('lessonType').isIn(['VIDEO', 'PDF', 'LIVE', 'QUIZ']).withMessage('نوع الدرس غير صالح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { title, description, content, unitId, order, lessonType, duration, isFree } = req.body;

    // Check if unit exists
    const unit = await prisma.unit.findUnique({
      where: { id: parseInt(unitId) }
    });

    if (!unit) {
      return res.status(400).json({
        success: false,
        message: 'الوحدة الدراسية غير موجودة'
      });
    }

    // Check if order already exists for this unit
    const existingLesson = await prisma.lesson.findFirst({
      where: {
        unitId: parseInt(unitId),
        order: parseInt(order)
      }
    });

    if (existingLesson) {
      return res.status(400).json({
        success: false,
        message: 'هذا الترتيب مستخدم بالفعل في هذه الوحدة'
      });
    }

    // Handle file uploads
    let videoUrl = null;
    let pdfUrl = null;

    if (req.files) {
      if (req.files.video) {
        videoUrl = `/uploads/videos/${req.files.video[0].filename}`;
      }
      if (req.files.pdf) {
        pdfUrl = `/uploads/pdfs/${req.files.pdf[0].filename}`;
      }
    }

    const lesson = await prisma.lesson.create({
      data: {
        title,
        description,
        content,
        videoUrl,
        pdfUrl,
        duration: duration ? parseInt(duration) : null,
        unitId: parseInt(unitId),
        order: parseInt(order),
        lessonType,
        isFree: isFree === 'true'
      },
      include: {
        unit: {
          include: {
            subject: {
              include: { grade: true }
            }
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الدرس بنجاح',
      data: { lesson }
    });

  } catch (error) {
    console.error('Create lesson error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إنشاء الدرس'
    });
  }
});

// @route   PUT /api/lessons/:id
// @desc    Update lesson
// @access  Private (Admin/Teacher)
router.put('/:id', [
  authenticateToken,
  isTeacherOrAdmin,
  upload.fields([
    { name: 'video', maxCount: 1 },
    { name: 'pdf', maxCount: 1 }
  ]),
  body('title').optional().notEmpty().trim().withMessage('عنوان الدرس مطلوب'),
  body('unitId').optional().isInt({ min: 1 }).withMessage('معرف الوحدة مطلوب'),
  body('order').optional().isInt({ min: 1 }).withMessage('ترتيب الدرس مطلوب'),
  body('lessonType').optional().isIn(['VIDEO', 'PDF', 'LIVE', 'QUIZ']).withMessage('نوع الدرس غير صالح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { title, description, content, unitId, order, lessonType, duration, isFree, isActive } = req.body;

    // Check if lesson exists
    const existingLesson = await prisma.lesson.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingLesson) {
      return res.status(404).json({
        success: false,
        message: 'الدرس غير موجود'
      });
    }

    // Check if unit exists (if updating unitId)
    if (unitId) {
      const unit = await prisma.unit.findUnique({
        where: { id: parseInt(unitId) }
      });

      if (!unit) {
        return res.status(400).json({
          success: false,
          message: 'الوحدة الدراسية غير موجودة'
        });
      }
    }

    // Check if order already exists (if updating order)
    if (order && (order !== existingLesson.order || unitId)) {
      const targetUnitId = unitId ? parseInt(unitId) : existingLesson.unitId;
      const lessonWithOrder = await prisma.lesson.findFirst({
        where: {
          unitId: targetUnitId,
          order: parseInt(order),
          id: { not: parseInt(id) }
        }
      });

      if (lessonWithOrder) {
        return res.status(400).json({
          success: false,
          message: 'هذا الترتيب مستخدم بالفعل في هذه الوحدة'
        });
      }
    }

    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (content !== undefined) updateData.content = content;
    if (unitId !== undefined) updateData.unitId = parseInt(unitId);
    if (order !== undefined) updateData.order = parseInt(order);
    if (lessonType !== undefined) updateData.lessonType = lessonType;
    if (duration !== undefined) updateData.duration = duration ? parseInt(duration) : null;
    if (isFree !== undefined) updateData.isFree = isFree === 'true';
    if (isActive !== undefined) updateData.isActive = isActive;

    // Handle file uploads
    if (req.files) {
      if (req.files.video) {
        updateData.videoUrl = `/uploads/videos/${req.files.video[0].filename}`;
      }
      if (req.files.pdf) {
        updateData.pdfUrl = `/uploads/pdfs/${req.files.pdf[0].filename}`;
      }
    }

    const updatedLesson = await prisma.lesson.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        unit: {
          include: {
            subject: {
              include: { grade: true }
            }
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'تم تحديث الدرس بنجاح',
      data: { lesson: updatedLesson }
    });

  } catch (error) {
    console.error('Update lesson error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تحديث الدرس'
    });
  }
});

// @route   DELETE /api/lessons/:id
// @desc    Delete lesson (soft delete)
// @access  Private (Admin/Teacher)
router.delete('/:id', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const lesson = await prisma.lesson.findUnique({
      where: { id: parseInt(id) }
    });

    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: 'الدرس غير موجود'
      });
    }

    // Soft delete
    await prisma.lesson.update({
      where: { id: parseInt(id) },
      data: { isActive: false }
    });

    res.json({
      success: true,
      message: 'تم حذف الدرس بنجاح'
    });

  } catch (error) {
    console.error('Delete lesson error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء حذف الدرس'
    });
  }
});

module.exports = router;