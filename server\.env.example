# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ostaz_saif_db"

# JWT
JWT_SECRET="your-super-secret-jwt-key-here-change-in-production"
JWT_EXPIRES_IN="7d"

# Server
PORT=5000
NODE_ENV="development"
FRONTEND_URL="http://localhost:3000"

# File Upload
MAX_FILE_SIZE=50000000
UPLOAD_PATH="./uploads"

# Email (Optional - for notifications)
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-app-password"
EMAIL_FROM="منصة أستاذ سيف <<EMAIL>>"

# Payment (Optional - for future integration)
PAYMENT_SECRET_KEY="your-payment-gateway-secret"
PAYMENT_PUBLIC_KEY="your-payment-gateway-public-key"

# Live Sessions (Optional)
ZOOM_API_KEY="your-zoom-api-key"
ZOOM_API_SECRET="your-zoom-api-secret"
