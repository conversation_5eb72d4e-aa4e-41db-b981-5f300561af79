const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../utils/db');
const { authenticateToken, isTeacherOrAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/subjects
// @desc    Get all subjects
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { gradeId } = req.query;
    const whereClause = { isActive: true };

    if (gradeId) {
      whereClause.gradeId = parseInt(gradeId);
    }

    const subjects = await prisma.subject.findMany({
      where: whereClause,
      include: {
        grade: true,
        units: {
          where: { isActive: true },
          orderBy: { order: 'asc' },
          include: {
            lessons: {
              where: { isActive: true },
              orderBy: { order: 'asc' }
            }
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    res.json({
      success: true,
      data: { subjects }
    });

  } catch (error) {
    console.error('Get subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب المواد الدراسية'
    });
  }
});

// @route   GET /api/subjects/:id
// @desc    Get single subject
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subject = await prisma.subject.findUnique({
      where: { id: parseInt(id) },
      include: {
        grade: true,
        units: {
          where: { isActive: true },
          orderBy: { order: 'asc' },
          include: {
            lessons: {
              where: { isActive: true },
              orderBy: { order: 'asc' }
            }
          }
        }
      }
    });

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: 'المادة الدراسية غير موجودة'
      });
    }

    res.json({
      success: true,
      data: { subject }
    });

  } catch (error) {
    console.error('Get subject error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب بيانات المادة الدراسية'
    });
  }
});

// @route   POST /api/subjects
// @desc    Create new subject
// @access  Private (Admin/Teacher)
router.post('/', [
  authenticateToken,
  isTeacherOrAdmin,
  body('name').notEmpty().trim().withMessage('اسم المادة مطلوب'),
  body('gradeId').isInt({ min: 1 }).withMessage('معرف الصف مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { name, description, gradeId } = req.body;

    // Check if grade exists
    const grade = await prisma.grade.findUnique({
      where: { id: parseInt(gradeId) }
    });

    if (!grade) {
      return res.status(400).json({
        success: false,
        message: 'الصف الدراسي غير موجود'
      });
    }

    const subject = await prisma.subject.create({
      data: {
        name,
        description,
        gradeId: parseInt(gradeId)
      },
      include: {
        grade: true
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء المادة الدراسية بنجاح',
      data: { subject }
    });

  } catch (error) {
    console.error('Create subject error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إنشاء المادة الدراسية'
    });
  }
});

// @route   PUT /api/subjects/:id
// @desc    Update subject
// @access  Private (Admin/Teacher)
router.put('/:id', [
  authenticateToken,
  isTeacherOrAdmin,
  body('name').optional().notEmpty().trim().withMessage('اسم المادة مطلوب'),
  body('gradeId').optional().isInt({ min: 1 }).withMessage('معرف الصف مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صالحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { name, description, gradeId, isActive } = req.body;

    // Check if subject exists
    const existingSubject = await prisma.subject.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingSubject) {
      return res.status(404).json({
        success: false,
        message: 'المادة الدراسية غير موجودة'
      });
    }

    // Check if grade exists (if updating gradeId)
    if (gradeId) {
      const grade = await prisma.grade.findUnique({
        where: { id: parseInt(gradeId) }
      });

      if (!grade) {
        return res.status(400).json({
          success: false,
          message: 'الصف الدراسي غير موجود'
        });
      }
    }

    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (gradeId !== undefined) updateData.gradeId = parseInt(gradeId);
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedSubject = await prisma.subject.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        grade: true
      }
    });

    res.json({
      success: true,
      message: 'تم تحديث المادة الدراسية بنجاح',
      data: { subject: updatedSubject }
    });

  } catch (error) {
    console.error('Update subject error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تحديث المادة الدراسية'
    });
  }
});

// @route   DELETE /api/subjects/:id
// @desc    Delete subject (soft delete)
// @access  Private (Admin/Teacher)
router.delete('/:id', [authenticateToken, isTeacherOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const subject = await prisma.subject.findUnique({
      where: { id: parseInt(id) }
    });

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: 'المادة الدراسية غير موجودة'
      });
    }

    // Soft delete
    await prisma.subject.update({
      where: { id: parseInt(id) },
      data: { isActive: false }
    });

    res.json({
      success: true,
      message: 'تم حذف المادة الدراسية بنجاح'
    });

  } catch (error) {
    console.error('Delete subject error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء حذف المادة الدراسية'
    });
  }
});

module.exports = router;